import { Checkbox } from "@heroui/react";
import { useFormContext } from "react-hook-form";
import { FormField } from "./FormField";
import type { ReactNode } from "react";

type CheckboxFieldProps = {
  name: string;
  children: ReactNode;
  disabled?: boolean;
  size?: "sm" | "md" | "lg";
  color?:
    | "default"
    | "primary"
    | "secondary"
    | "success"
    | "warning"
    | "danger";
  className?: string;
};

export const CheckboxField = ({
  name,
  children,
  disabled,
  size = "md",
  color = "primary",
  className,
}: CheckboxFieldProps) => {
  const {
    formState: { errors },
  } = useFormContext();

  const error = errors[name];

  return (
    <FormField name={name} disabled={disabled}>
      {({ value, onChange }) => (
        <div className={className}>
          <Checkbox
            isSelected={!!value}
            onValueChange={onChange}
            disabled={disabled}
            size={size}
            color={color}
            isInvalid={!!error}
          >
            {children}
          </Checkbox>
          {error && (
            <p className="text-tiny text-danger mt-1">
              {error.message as string}
            </p>
          )}
        </div>
      )}
    </FormField>
  );
};
