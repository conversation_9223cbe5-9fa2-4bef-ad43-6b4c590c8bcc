import React from 'react'
import { Hero<PERSON><PERSON>rovider } from '@heroui/react'
import { ToastProvider } from '@heroui/toast'
import { ThemeProvider as NextThemesProvider } from 'next-themes'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
    mutations: {
      retry: 1,
    },
  },
})

type Props = {
  children: React.ReactNode
}

export const Providers = ({ children }: Props) => {
  return (
    <QueryClientProvider client={queryClient}>
      <NextThemesProvider attribute="class" defaultTheme="system">
        <HeroUIProvider>
          <ToastProvider />
          {children}
        </HeroUIProvider>
      </NextThemesProvider>
    </QueryClientProvider>
  )
}