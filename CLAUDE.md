# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a session-based authentication system with a full-stack implementation:

- **Backend**: Node.js + Express + PostgreSQL with session-based authentication
- **Frontend**: React 19 + TypeScript + Vite + Tailwind CSS + HeroUI
- **Architecture**: Monorepo structure with separate frontend and backend directories

## Development Commands

### Main Project Commands (from root)

```bash
npm run dev              # Start both frontend and backend in development
npm run dev:frontend     # Start only frontend (cd frontend && npm run dev)
npm run dev:backend      # Start only backend (cd backend && npm run dev)
npm run build            # Build both frontend and backend
npm run build:frontend   # Build only frontend
npm run build:backend    # Build only backend
npm run test             # Run tests for both frontend and backend
npm run test:frontend    # Run frontend tests
npm run test:backend     # Run backend tests
npm run lint             # Lint both frontend and backend
npm run lint:frontend    # Lint frontend code
npm run lint:backend     # Lint backend code
```

### Individual Package Commands

Navigate to `frontend/` or `backend/` directories to run individual package commands.

## Architecture Overview

### Backend Architecture (Node.js + Express + PostgreSQL)

Based on the PRD document, the backend implements:

**Key Technologies:**

- Express.js with TypeScript for API server
- PostgreSQL with Kysely for type-safe database queries
- Session-based authentication using express-session + connect-pg-simple
- bcryptjs for password hashing
- Passport.js for OAuth (Google) integration
- Comprehensive security middleware (helmet, cors, rate limiting)

**Database Schema:**

- `users` - User accounts with profile information
- `session` - Express session storage in PostgreSQL
- `email_verification_tokens` - Email verification workflow
- `password_reset_tokens` - Password reset workflow
- `login_history` - Audit trail for login attempts
- `account_lockouts` - Account security management

**API Structure:**

- `/api/auth/*` - Authentication endpoints (login, register, logout, OAuth)
- `/api/user/*` - User profile and session management
- Session-based authentication with PostgreSQL session store
- OWASP-compliant security implementation

### Frontend Architecture (React 19 + TypeScript)

Based on the task breakdown document, the frontend implements:

**Key Technologies:**

- React 19 with TypeScript for modern React features
- Vite for build tooling and development server
- Tailwind CSS + HeroUI for UI components and design system
- React Router v7 for client-side routing
- React Query for server state management and caching
- Zod for form validation and schema validation

**Architecture Patterns:**

- **Server State Management**: React Query handles all API interactions and caching
- **Component Architecture**: HeroUI-based design system with custom components
- **Route Protection**: ProtectedRoute wrapper using useUserProfile for auth checks

**Key Routes:**

- `/` - Landing page
- `/login` - Login form with OAuth options
- `/register` - Registration form
- `/forgot-password` - Password reset initiation
- `/reset-password/:token` - Password reset completion
- `/verify-email/:token` - Email verification
- `/profile` - User dashboard and profile display
- `/settings` - Account settings with multiple tabs

## Development Workflow

1. **Setup**: Run `npm run dev` from root to start both frontend and backend
2. **Backend Development**: The backend should follow the comprehensive PRD requirements
3. **Frontend Development**: Follow the task breakdown for feature implementation
4. **Database**: PostgreSQL setup required for session storage and user data
5. **Environment**: Both frontend and backend require proper .env configuration

## Key Implementation Notes

- **Authentication Flow**: Session-based (not JWT) using PostgreSQL session storage
- **Security**: Implements OWASP best practices with account lockouts, rate limiting, and secure session management
- **Database**: All queries should use Kysely for type safety
- **Email System**: Verification and password reset flows with email templates
- **File Upload**: Profile picture upload with image processing via Cloudinary
- **OAuth**: Google OAuth integration with account linking capabilities
- **Validation**: Shared Zod schemas between frontend and backend when possible

## Important Security Considerations

- Generic error messages to prevent user enumeration
- Account lockout after failed login attempts with exponential backoff
- Comprehensive rate limiting on all authentication endpoints
- HTTPS-only in production with secure session cookies
- Input validation and sanitization on all user inputs
- Comprehensive logging for security events and authentication attempts

## File Structure Notes

- `frontend/` contains React application
  src/
  ├── components/
  │ ├── ui/ # Basic UI components (Button, Modal, Avatar, etc.)
  │ ├── forms/ # Form field components with react-hook-form
  │ └── layout/ # Layout components (Header, Footer, AuthLayout, etc.)
  ├── app/
  │ ├── providers.tsx # All app providers (Auth, Query, Theme etc.)
  │ ├── router.tsx # Route definitions and navigation structure
  │ ├── index.tsx # App entry point that wraps router with providers
  │ ├── public/ # Pages accessible without authentication
  │ │ └── login/ # Example: Login feature
  │ │ ├── components/ # Login-specific components
  │ │ ├── hooks/ # Login-specific hooks
  │ │ └── LoginPage.tsx # Main login page
  │ └── private/ # Pages that require user authentication
  │ └── profile/ # Example: Profile feature
  │ ├── components/ # Profile-specific components
  │ ├── hooks/ # Profile-specific hooks
  │ └── ProfilePage.tsx # Main profile page
  ├── hooks/ # Global custom React hooks
  ├── lib/ # Configurations and integrations
  │ ├── api/ # API configuration and calls
  │ │ ├── index.ts # Base axios configuration
  │ │ ├── auth.ts # Authentication API calls
  │ │ └── user.ts # User management API calls
  │ └── validation.ts # Zod schemas for form validation
  ├── utils/ # Pure utility functions and helpers
  └── main.tsx # React app entry point

- `backend/` contains Node.js Express API (when implemented)
- Root `package.json` orchestrates both with concurrent development commands
- Each directory should have its own `package.json` with specific dependencies

## Code Quality

- Write TypeScript with strict type safety - no `any` types
- Use meaningful variable and function names that describe their purpose
- Keep functions small and focused on a single responsibility
- Don't add any comments in the codebase
- Always use type instead of interface when declaring a type
- Preferring using arrow function and exporting directly instead of export default
- Don't use barrel files for exporting/importing
- onPress should be used instead of onClick when using buttons from HeroUI
- Use React.ReactNode instead of importing ReactNode from React
