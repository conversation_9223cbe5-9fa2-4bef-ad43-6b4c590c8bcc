import { Input } from '@heroui/react'
import { useFormContext } from 'react-hook-form'
import { FormField } from './FormField'
import type { ReactNode } from 'react'

type InputFieldProps = {
  name: string
  label?: string
  placeholder?: string
  type?: 'text' | 'email' | 'password' | 'tel' | 'url'
  disabled?: boolean
  required?: boolean
  startContent?: ReactNode
  endContent?: ReactNode
  description?: string
  className?: string
}

export const InputField = ({
  name,
  label,
  placeholder,
  type = 'text',
  disabled,
  required,
  startContent,
  endContent,
  description,
  className
}: InputFieldProps) => {
  const {
    formState: { errors }
  } = useFormContext()

  const error = errors[name]
  const errorMessage = error?.message as string | undefined

  return (
    <FormField name={name} disabled={disabled}>
      {({ value, onChange, onBlur }) => (
        <Input
          label={label}
          placeholder={placeholder}
          type={type}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          onBlur={onBlur}
          disabled={disabled}
          required={required}
          isRequired={required}
          isInvalid={!!error}
          errorMessage={errorMessage}
          startContent={startContent}
          endContent={endContent}
          description={description}
          className={className}
          classNames={{
            input: 'bg-transparent',
            inputWrapper: 'bg-default-100/60 backdrop-blur-md'
          }}
        />
      )}
    </FormField>
  )
}