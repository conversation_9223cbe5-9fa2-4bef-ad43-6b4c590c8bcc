import type { ReactNode } from "react";
import {
  FormProvider,
  useForm,
  type DefaultValues,
  type FieldValues,
  type SubmitHandler,
} from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import type { ZodObject } from "zod";

type Props<T extends FieldValues> = {
  schema: ZodObject<T>;
  onSubmit: SubmitHandler<T>;
  children: ReactNode;
  className?: string;
  defaultValues?: DefaultValues<T>;
};

export const Form = <T extends FieldValues>({
  schema,
  onSubmit,
  children,
  className,
  defaultValues,
}: Props<T>) => {
  const form = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues,
    mode: "onChange",
  });

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className={className}>
        {children}
      </form>
    </FormProvider>
  );
};
