import type { ReactNode } from 'react'
import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Controller, type FieldPath, type FieldValues } from 'react-hook-form'

type FormFieldProps<T extends FieldValues> = {
  name: FieldPath<T>
  children: (field: {
    value: any
    onChange: (value: any) => void
    onBlur: () => void
    disabled?: boolean
  }) => ReactNode
  disabled?: boolean
}

export const FormField = <T extends FieldValues>({
  name,
  children,
  disabled
}: FormFieldProps<T>) => {
  const { control } = useFormContext<T>()

  return (
    <Controller
      name={name}
      control={control}
      disabled={disabled}
      render={({ field }) => children(field)}
    />
  )
}