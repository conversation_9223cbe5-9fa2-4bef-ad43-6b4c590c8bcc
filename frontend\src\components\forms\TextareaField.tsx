import { Textarea } from '@heroui/react'
import { useFormContext } from 'react-hook-form'
import { FormField } from './FormField'

type TextareaFieldProps = {
  name: string
  label?: string
  placeholder?: string
  disabled?: boolean
  required?: boolean
  description?: string
  className?: string
  minRows?: number
  maxRows?: number
}

export const TextareaField = ({
  name,
  label,
  placeholder,
  disabled,
  required,
  description,
  className,
  minRows = 3,
  maxRows = 8
}: TextareaFieldProps) => {
  const {
    formState: { errors }
  } = useFormContext()

  const error = errors[name]
  const errorMessage = error?.message as string | undefined

  return (
    <FormField name={name} disabled={disabled}>
      {({ value, onChange, onBlur }) => (
        <Textarea
          label={label}
          placeholder={placeholder}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          onBlur={onBlur}
          disabled={disabled}
          required={required}
          isRequired={required}
          isInvalid={!!error}
          errorMessage={errorMessage}
          description={description}
          className={className}
          minRows={minRows}
          maxRows={maxRows}
          classNames={{
            input: 'bg-transparent',
            inputWrapper: 'bg-default-100/60 backdrop-blur-md'
          }}
        />
      )}
    </FormField>
  )
}