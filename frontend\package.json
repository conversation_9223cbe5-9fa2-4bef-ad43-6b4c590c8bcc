{"name": "session-authentication-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"@heroui/button": "^2.2.24", "@heroui/input-otp": "^2.1.24", "@heroui/react": "^2.8.2", "@heroui/switch": "^2.2.22", "@heroui/system": "^2.4.20", "@heroui/theme": "^2.4.20", "@heroui/toast": "^2.0.14", "@hookform/resolvers": "^5.2.1", "@tanstack/react-query": "^5.84.1", "axios": "^1.11.0", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.535.0", "next-themes": "^0.4.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.7.1", "tailwind-merge": "^3.3.1", "zod": "^4.0.14"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}