import { useState } from 'react'
import { Input, Button } from '@heroui/react'
import { Eye, EyeOff } from 'lucide-react'
import { useFormContext } from 'react-hook-form'
import { FormField } from './FormField'

type PasswordInputProps = {
  name: string
  label?: string
  placeholder?: string
  disabled?: boolean
  required?: boolean
  description?: string
  className?: string
}

export const PasswordInput = ({
  name,
  label = 'Password',
  placeholder = 'Enter your password',
  disabled,
  required,
  description,
  className
}: PasswordInputProps) => {
  const [isVisible, setIsVisible] = useState(false)
  const {
    formState: { errors }
  } = useFormContext()

  const error = errors[name]
  const errorMessage = error?.message as string | undefined

  const toggleVisibility = () => setIsVisible(!isVisible)

  return (
    <FormField name={name} disabled={disabled}>
      {({ value, onChange, onBlur }) => (
        <Input
          label={label}
          placeholder={placeholder}
          type={isVisible ? 'text' : 'password'}
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          onBlur={onBlur}
          disabled={disabled}
          required={required}
          isRequired={required}
          isInvalid={!!error}
          errorMessage={errorMessage}
          description={description}
          className={className}
          classNames={{
            input: 'bg-transparent',
            inputWrapper: 'bg-default-100/60 backdrop-blur-md'
          }}
          endContent={
            <Button
              className="focus:outline-none"
              type="button"
              onPress={toggleVisibility}
              size="sm"
              variant="light"
              isIconOnly
            >
              {isVisible ? (
                <EyeOff className="h-4 w-4 text-default-400 pointer-events-none" />
              ) : (
                <Eye className="h-4 w-4 text-default-400 pointer-events-none" />
              )}
            </Button>
          }
        />
      )}
    </FormField>
  )
}